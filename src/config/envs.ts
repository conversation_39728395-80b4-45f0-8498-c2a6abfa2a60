/**
 * Uses `env` variables from env file for easy access
 * of `env` instead of typing `import.meta.env`
 */
export const envs = {
  // Locize
  locizeProjectKey: import.meta.env?.VITE_LOCIZE_PROJECT_ID,
  locizeApiKey: import.meta.env?.VITE_LOCIZE_API_KEY,

  // Mixpanel
  mixPanelApiKey: import.meta.env?.VITE_MIX_PANEL_API_KEY,
  mixPanelProxy: import.meta.env?.VITE_MIX_PANEL_PROXY,

  // App wide
  environment: import.meta.env?.VITE_ENVIRONMENT,
  port: import.meta.env?.VITE_PORT,
  host: import.meta.env?.VITE_HOST,
  buildVariation: import.meta.env?.VITE_BUILD_VARIATION,
  instrumentCode: import.meta.env?.VITE_INSTRUMENT_CODE,
  envColor: import.meta.env?.VITE_APP_ENV_COLOR,
  roboBackendUrl: import.meta.env?.VITE_ROBO_BACKEND_URL,
  roboEmailEnv: import.meta.env?.VITE_ROBO_EMAIL_ENV,
  analyticsTrack: import.meta.env?.VITE_ANALYTICS_TRACK,
} as const
