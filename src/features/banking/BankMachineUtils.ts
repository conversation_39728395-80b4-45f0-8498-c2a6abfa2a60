import { fromPromise } from 'xstate'
import { BankMachineContext, Events } from './types/BankMachineTypes.type'

/**
 * Wraps a formerly known as BankMachine "service" functions from xstate4 to
 * newly actor pattern using `fromPromise` in xstate5
 */
const bankPromiseToPromiseActor = <T>(
  promiseFunction: (context: BankMachineContext, event: Events) => Promise<T>
) =>
  fromPromise(
    ({
      input,
    }: {
      input: { context: BankMachineContext; event: Events }
    }) => promiseFunction(input?.context, input?.event)
  )

/**
 * Util function used for providing inputs to promise actors. Generally typed,
 * will be broken down to actual statically typed arguments
 */
const bankPromiseActorInput = <T>(params: T) => ({
  ...params,
})

export { bankPromiseActorInput, bankPromiseToPromiseActor }
