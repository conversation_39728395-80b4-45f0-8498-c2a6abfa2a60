import { useState } from 'react'
import TontineModal from '../../../common/components/Modal'
import Card from '../../../common/components/card/Card'
import { ASSET } from '../../../common/constants/Assets'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import UserFeedback from './UserFeedback'

/** Feedback card only rendered when the user is in desktop mode and
 * authenticated. When clicked opens a modal used to provide feedback
 * about the user experience with the app
 */
const FeedbackCard = () => {
  const t = useTranslate()
  const { isAuthenticated } = useAccountService()
  const [openModal, setOpenModal] = useState(false)

  if (isAuthenticated) {
    return (
      <>
        <Card
          headerImage={ASSET.iconmileureymodal}
          title={t('FEEDBACK_CARD.TITLE')}
          showArrow
          onClick={() => setOpenModal(true)}
          interactEnabled
          variant="feedback"
        />

        {openModal && (
          <TontineModal
            // Renders the `<UserFeedback />` component as a modal
            isOpen={openModal}
            backdrop
            hasOnlyContent
            onOutsideModalContentClick={() => setOpenModal(false)}
          >
            <UserFeedback
              ratingOpenDefault
              closeModal={() => setOpenModal(false)}
              onSuccessfulSubmit={() => setOpenModal(false)}
            />
          </TontineModal>
        )}
      </>
    )
  }

  return <></>
}

export default FeedbackCard
