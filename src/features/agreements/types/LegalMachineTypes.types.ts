import { EventWithCommonPayload } from '../../CommonState.type'
import { Events } from '../../banking/types/BankMachineTypes.type'

/**
 * All possible form types that can be used to fetch, submit and update
 * investment forms
 */
type InvestmentAccountFormType = 'InvestmentAccountOpening'

/**
 * Agreement types
 */
type Agreement = 'TermsAndConditions'
type TontineProduct = 'TontineIRA'

/**
 * Supported events by the legal machine
 */
type EventType =
  | 'FETCH_FORM'
  | 'SAVE_FORM_PROGRESS'
  | 'SUBMIT_FORM'
  | 'FETCH_AGREEMENT'
  | 'SIGN_AGREEMENT'
  | 'GENERATE_FORM_AS_PDF'

type AgreementForInvestmentAccount = Agreement
type AgreementDataForInvestmentAccount = Pick<
  AgreementData,
  AgreementForInvestmentAccount
>

type AgreementContents = {
  content: Array<{ contents: string; tag: 'Text' | 'Image' }>
  title: string
  version: number
  userAgreed: Date | null
}

type AgreementParsedContents = {
  title: string
  version: number
  text: string
  userAgreed: string | null
  image?: string
  heading?: string
}

type AgreementData = {
  [key in Agreement]: AgreementParsedContents
}

interface LegalMachineEventPayload extends EventWithCommonPayload {
  product?: TontineProduct
  agreementTypes?: Array<Agreement>
  signedAgreementData?: {
    checkboxChecked?: boolean
    signedAgreementContents: AgreementData
  }
  agreementType?: Agreement
}

type LegalMachineContext = {
  agreement?: AgreementData
}
/**
 * Usually used in `onDone` when a service resolves and the data from the
 * service is returned
 */
type ServiceResponseData = AgreementData

/**
 * Used to adding a type to `event` in every action, service and guard `payload`
 * property is again present in the `data` property, because there are scenarios
 * where we return the passed in event payload and that payload is resolved via
 * a promise, because most of the backend APIs don't return data on successful
 * response
 */
interface LegalMachineEvent {
  type: EventType
  payload?: LegalMachineEventPayload
  // Covers scenarios where the data is returned from the frontend, or some API
  // responds with data
  output?: ServiceResponseData & { payload?: LegalMachineEventPayload } & {
    formType: InvestmentAccountFormType
    agreementType: Agreement
    pdfBase64: string
    authToken?: string
  }
}

/**
 * All states that the bank machine can be in
 */
type States = {
  FETCHING_AGREEMENT: 'FETCHING_AGREEMENT'
  SIGNING_AGREEMENT: 'SIGNING_AGREEMENT'
}

export type {
  Agreement,
  AgreementContents,
  AgreementData,
  AgreementDataForInvestmentAccount,
  AgreementForInvestmentAccount,
  Events,
  InvestmentAccountFormType,
  LegalMachineContext,
  LegalMachineEvent,
  LegalMachineEventPayload,
  States,
  TontineProduct,
}
