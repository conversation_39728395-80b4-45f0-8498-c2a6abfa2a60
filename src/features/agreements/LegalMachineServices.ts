import { <PERSON>uff<PERSON> } from 'buffer'
import axios from 'axios'
import { API } from '../../common/api/API'
import { axiosConfig } from '../../common/api/RequestConfig'
import { API_STATUS } from '../../common/constants/ApiErrors'
import { writeToConsoleAndIssueAlert } from '../../common/utils/UtilFunctions'
import { isLite } from '../../config/lite'
import { getAuthToken } from '../authentication/utils/AuthFunctions'
import { parsePayloadToParams, processAndFlatAgreement } from './LegalUtils'
import {
  Agreement,
  AgreementContents,
  LegalMachineContext,
  LegalMachineEvent,
} from './types/LegalMachineTypes.types'

const withCredentials = false

/**
 * Fetches an agreement containing legal text
 */
const fetchAgreement = async (
  _: LegalMachineContext,
  event: LegalMachineEvent
) => {
  try {
    const { agreementTypes } = event?.payload as {
      agreementTypes: Array<Agreement>
    }

    if (agreementTypes?.length <= 0) {
      throw new TypeError(
        `At least 1 agreement type is needed in order to get an agreement`
      )
    }

    const response = await axios.get(
      `${API.getAgreement}/${parsePayloadToParams(agreementTypes)}/latest`,
      axiosConfig({
        signal: event?.payload?.abortController?.signal,
        withCredentials,
        authToken: isLite ? undefined : getAuthToken(),
      })
    )

    const { status, data } = response as {
      status: number
      data: Array<[Agreement, AgreementContents]>
    }

    const processedResponse = processAndFlatAgreement(data)

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.(processedResponse)

      return processedResponse
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.abortController?.abort()
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Signs an agreement using an unique agreement type
 */
const signAgreement = async (
  _: LegalMachineContext,
  event: LegalMachineEvent
) => {
  try {
    if (
      !event?.payload ||
      !event?.payload?.signedAgreementData ||
      !event?.payload?.agreementType
    ) {
      throw new TypeError(
        `No agreementData or type found, check payload got >> ${JSON.stringify(event?.payload)} << `
      )
    }

    const { signedAgreementData, agreementType } = event.payload

    const version =
      signedAgreementData?.signedAgreementContents?.[agreementType]?.version

    const response = await axios.put(
      `${API.signAgreement}/${agreementType}/${version}`,
      null,
      axiosConfig({ withCredentials, authToken: getAuthToken() })
    )

    const { status } = response

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.(event?.payload)

      return event?.payload
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Fetches a PDF version of a submitted form with data
 */
const getFormAsPdf = async (
  _: LegalMachineContext,
  event: LegalMachineEvent
) => {
  try {
    if (!event?.output) {
      throw new TypeError(`No data received from previous event`)
    }

    // Different event data on different call. For the submit call we don't have this
    let formType = Object.keys(event.output)[0]

    // Only present when user submits the form
    if (event?.output?.formType) {
      formType = event?.output?.formType
    }

    // Ideally should be Crawford on both
    const translateAtm: { [key: string]: string } = {
      InvestmentAccountOpening: 'Crawford',
    }

    const response = await axios.get(
      `${API.readInvestmentFormFields}/${translateAtm[formType]}/generate`,
      {
        ...axiosConfig({ withCredentials, authToken: getAuthToken() }),
        // Needs to be in binary!, otherwise the encoding is messed up
        // and will be broken
        responseType: 'arraybuffer',
      }
    )
    const { status, data } = response as {
      status: number
      data: string
    }

    const jsonObject = {
      // Decodes the binary pdf data to base64 string so it can be used in an iframe
      pdfBase64: Buffer.from(data, 'binary').toString('base64'),
      formType,
    }

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.(jsonObject)

      return jsonObject
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

export { fetchAgreement, getFormAsPdf, signAgreement }
