import { ReactNode, useLayoutEffect } from 'react'
import { useSearchParams } from 'react-router-dom'
import SuspenseLoader from '../../common/components/SuspenseLoader'
import {
  postMessageToParent,
  useMessageListener,
} from '../../common/hooks/useWindowPostMessage'
import { COMMON_CONSTANTS } from '../../common/utils/consts'
import { LiteContext } from './LiteAuthContext'
import { useAccountService } from './hooks/useAccountService'
import { States } from './types/AuthMachineTypes.type'
import { getAuthToken } from './utils/AuthFunctions'

/**
 * Auth provider for Lite version of MyTontine.
 * - Verifies MTL email and gets user data
 * - Iframe listeners listen for events from the tontine.com website
 */
const LiteAuthProvider = ({ children }: { children: ReactNode }) => {
  const { send, currentState, context } = useAccountService()
  const [searchParams] = useSearchParams()
  const websiteOrigin = searchParams.get(
    COMMON_CONSTANTS.TONTINE_WEBSITE_ORIGIN
  )
  const verify_token = searchParams.get('ver') || ''

  useLayoutEffect(() => {
    send({
      type: 'VERIFY_EMAIL_MTL',
      payload: {
        websiteOrigin,
        // do not send verify token if already authed, it will
        // cause stale token error
        verifyToken: getAuthToken() ? undefined : verify_token,
        successCallback: (data) => {
          // Sends a parent (page that embeds this via iframe) an event
          postMessageToParent({
            eventId: 'SUCCESS_VERIFY',
            eventData: data as object,
            origin: websiteOrigin,
          })
        },
        failureCallback: () =>
          postMessageToParent({
            eventId: 'TERMINATE_WEB_SESSION',
            origin: websiteOrigin,
          }),
      },
    })
  }, [])

  // Message to terminate session is issued, propagate the whole isAuth state
  useMessageListener(({ eventId, source, origin }) => {
    if (eventId === 'TERMINATE_REF_SESSION') {
      send({
        type: 'TERMINATE_MTL_SESSION',
        payload: {
          successCallback: () => {
            // Reply to the website that session has been terminated
            source?.postMessage(
              {
                payload: { eventId: 'TERMINATE_SUCCESS' },
              },
              {
                targetOrigin: origin,
              }
            )
          },
        },
      })
    }
  })

  return (
    <LiteContext.Provider
      value={{
        isAuth: context?.liteData?.isAuthenticated,
        referralDetails: context?.liteData?.referralDetails,
        litePensionPlan: context?.liteData?.pensionPlan,
        error: context?.liteData?.error,
        isLoading: currentState === 'VERIFYING_EMAIL_MTL',
        verifyToken: verify_token,
        currentState: currentState as unknown as States,
      }}
    >
      {currentState === 'VERIFYING_EMAIL_MTL' ? <SuspenseLoader /> : children}
    </LiteContext.Provider>
  )
}

export default LiteAuthProvider
