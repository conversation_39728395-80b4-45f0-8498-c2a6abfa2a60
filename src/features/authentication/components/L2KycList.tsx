import Card from '../../../common/components/card/Card'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { ACCOUNT_MENU } from '../../../routes/Route'
import { useAccountService } from '../hooks/useAccountService'

/**
 * Renders a list of CTA cards for L2 KYC, and shows the user their L2 KYC
 * Status
 */
const L2KycList = () => {
  const t = useTranslate()
  const navigate = useCustomNavigation()
  const { context } = useAccountService()
  const { user_details } = context
  const address_verified =
    user_details?.kyc_status?.L2?.requirements?.address_verified

  return (
    <>
      <Card
        title={t('KYC.ADDRESS_VERIFIED')}
        onClick={
          address_verified
            ? undefined
            : () => navigate(ACCOUNT_MENU.ADDRESS_VERIFICATION)
        }
        showArrow
        arrowInvisible={address_verified}
        interactEnabled={!address_verified}
        variant="gray-dirty"
        alert={address_verified ? 'completed' : 'warn'}
      />
    </>
  )
}

export default L2KycList
