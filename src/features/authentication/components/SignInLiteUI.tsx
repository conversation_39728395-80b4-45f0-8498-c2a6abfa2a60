import Button from '../../../common/components/Button'
import Modal from '../../../common/components/Modal'
import TextInput from '../../../common/components/TextInput'
import TimerButton from '../../../common/components/TimerButton'
import ConfirmationModal from '../../../common/components/confirmation-modal/ConfirmationModal'
import { ANIMATION } from '../../../common/constants/Animations'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { INPUT_LIMIT } from '../../../common/constants/InputLimits'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { useLiteSignIn } from '../hooks/useLiteSignIn'
import style from '../style/SignInLIteUI.module.scss'
import { millisecondsToSeconds } from '../utils/UtilsFunctions'
import { AUTH_CONSTANTS } from '../utils/consts'

type SignInLIteUIProps = {
  isOpenMobile?: boolean
  setIsOpenMobile?: (isOpen: boolean) => void
  asModal?: boolean
}

/**
 * Renders a text input and a button for signing in to the MyTontine
 */
const SignInLiteUI = ({
  isOpenMobile,
  setIsOpenMobile,
  asModal,
}: SignInLIteUIProps) => {
  const t = useTranslate()

  const {
    onSignInClick,
    emailValidated,
    validateEmail,
    setUserEmail,
    currentState,
    userEmail,
    isOpenConfirm,
    dismissConfirmModal,
  } = useLiteSignIn()

  const signInUi = (
    <>
      <TextInput
        type="email"
        height="auto-h"
        value={userEmail}
        onChange={setUserEmail}
        onKeyDown={
          // Prevents the button being clicked when the enter key is pressed
          // if the button is in invalid state
          emailValidated?.valid
            ? ({ code }) => {
                if (code === 'Enter') {
                  onSignInClick()
                }
              }
            : undefined
        }
        errorMessage={emailValidated}
        autoComplete={'on'}
        maxLength={INPUT_LIMIT.GENERIC_MAX}
        placeholder={t('EMAIL_PLACEHOLDER_TEXT')}
        validatorFunction={validateEmail}
        label={''}
        className={style['sign-in-lite-ui__input']}
        dataTestID={UI_TEST_ID.emailInput}
        trackActivity={{
          trackId: 'email_login_modal_email',
        }}
      />
      <Button
        disabled={!emailValidated?.valid}
        onClick={onSignInClick}
        loading={currentState === 'SENDING_NEW_TAB_EMAIL'}
        className={style['sign-in-lite-ui__button']}
        dataTestID={UI_TEST_ID.signUpHomeBtn}
        trackActivity={{
          trackId: 'email_login_modal_send_login_email',
        }}
      >
        {t('LOGIN_EMAIL_BTN_LABEL')}
      </Button>
    </>
  )

  return (
    <section className={style['sign-in-lite-ui']}>
      {asModal && !isOpenConfirm && (
        <Modal
          isOpen={isOpenMobile ?? false}
          onOutsideModalContentClick={() => setIsOpenMobile?.(false)}
          onClickCloseButton={() => setIsOpenMobile?.(false)}
          backdrop
          showCloseButton
          wide
          trackActivityBackDrop={{
            trackId: 'email_login_modal_close_via_backdrop',
          }}
          trackActivityCloseButton={{
            trackId: 'email_login_modal_close',
          }}
        >
          <h3 className={style['sign-in-lite-ui__header']}>
            {t('SIGN_IN_FOR_STATS')}
          </h3>
          {signInUi}
        </Modal>
      )}

      {!asModal && signInUi}

      <ConfirmationModal
        isOpen={isOpenConfirm}
        title={'SUCCESS.HEADERTEXT.MODAL'}
        content={t('MODAL.BODY.TEXT.VERIFICATION.SENT')}
        contentValues={{
          email: userEmail,
        }}
        animatedIcon={ANIMATION.checkmark}
      >
        <Button
          onClick={() => {
            // Makes sure the login UI stays closed even after this modal has been closed
            setIsOpenMobile?.(false)
            dismissConfirmModal()
          }}
          trackActivity={{ trackId: 'success_modal_dismiss' }}
        >
          {t('SUCCESS.MODAL.DISMISS.BUTTON')}
        </Button>

        <TimerButton
          trackActivity={{ trackId: 'success_modal_resend_email' }}
          seconds={millisecondsToSeconds(
            AUTH_CONSTANTS.RESEND_EMAIL_TIMER_MILLISECONDS
          )}
          variant="alternative"
          onClick={onSignInClick}
          disabled={currentState !== 'READY_RESEND_EMAIL'}
        >
          {t('ACCOUNT.EMAIL_SEND_AGAIN_BUTTON')}
        </TimerButton>
      </ConfirmationModal>
    </section>
  )
}

export default SignInLiteUI
