import React, { useEffect, useRef } from 'react'
import Input<PERSON>abel from '../../../common/components/InputLabel'
import style from '../style/PinInput.module.scss'
import { PinInputProps } from '../types/Pin.types'
import { AUTH_CONSTANTS } from '../utils/consts'

/**
 * A component that renders a PIN input field with a specified number of input boxes.
 *
 * Each input box represents a single digit of the PIN and automatically focuses on
 * the next box as digits are entered. The component also supports pasting a full
 * PIN into the fields. It handles key events to facilitate navigation and input
 * correction within the PIN fields.
 */
const PinInput = ({
  label,
  pinLength = AUTH_CONSTANTS.PIN_INPUT_FIELDS,
  values,
  autoFocus = true,
  autoComplete = 'off',
  type = 'password',
  onChange,
  errorMessage,
  inputRefs,
  children,
}: PinInputProps) => {
  const localInputsRef = useRef<HTMLInputElement[]>([])

  const handleRef = (el: HTMLInputElement | null, index: number) => {
    if (el) {
      localInputsRef.current[index] = el
      if (inputRefs) {
        inputRefs.current[index] = el
      }
    }
  }

  useEffect(() => {
    if (autoFocus && localInputsRef.current[0]) {
      localInputsRef.current[0].focus()
    }
  }, [autoFocus])

  const handleKeyDown = (e: React.KeyboardEvent, index: number) => {
    if (e.key >= '0' && e.key <= '9') {
      const newValues = [...values]
      newValues[index] = e.key
      onChange(newValues, index)

      if (index < pinLength - 1) {
        localInputsRef.current[index + 1]?.focus()
      }
      e.preventDefault()
    } else if (e.key === 'Backspace') {
      const newValues = [...values]
      newValues[index] = ''
      onChange(newValues, index)

      if (index > 0) {
        localInputsRef.current[index - 1]?.focus()
      }
      e.preventDefault()
    }
  }
  const handlePaste = (e: React.ClipboardEvent, index: number) => {
    e.preventDefault()
    const pasteData = e.clipboardData.getData('text/plain')
    const newValues = [...values]
    pasteData
      .split('')
      .slice(0, pinLength)
      .forEach((char, i) => {
        if (index + i < pinLength && /^[0-9]$/.test(char)) {
          newValues[index + i] = char
        }
      })
    onChange(newValues, index)
  }

  return (
    <article className={style.pinInput}>
      {label && (
        <InputLabel className={style.pinInput__pinInputLabel} label={label} />
      )}

      <div className={style.pinInput__container}>
        {Array.from({ length: pinLength }).map((_, index) => (
          <input
            key={`pin-${index}`}
            id={`pi${index}`}
            type={type}
            inputMode="numeric"
            className={`${style.pinInput__field} ${errorMessage ? style['pinInput__field--error'] : ''}`}
            pattern="[0-9]*"
            maxLength={1}
            value={values[index] || ''}
            ref={(el) => handleRef(el, index)}
            onKeyDown={(e) => handleKeyDown(e, index)}
            defaultValue={undefined}
            autoCorrect="off"
            autoCapitalize="off"
            autoComplete={autoComplete}
            spellCheck={false}
            // Useless onChange to satisfy react error
            onChange={() => undefined}
            onPaste={(e) => handlePaste(e, index)}
          />
        ))}
      </div>
      {children}
    </article>
  )
}

export default PinInput
