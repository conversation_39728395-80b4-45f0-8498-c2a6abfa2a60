import Layout from '../../../common/components/Layout'
import NavigationButtons from '../../../common/components/NavigationButtons'
import PageContent from '../../../common/components/PageContent'
import TextError from '../../../common/components/TextError'
import ConfirmationModal from '../../../common/components/confirmation-modal/ConfirmationModal'
import { ANIMATION } from '../../../common/constants/Animations'
import { ASSET } from '../../../common/constants/Assets'
import { useTranslate } from '../../../common/hooks/useTranslate'
import PinConfirmation from '../components/PinConfirmation'
import PinInput from '../components/PinInput'
import { useAccountService } from '../hooks/useAccountService'
import style from '../style/PinInput.module.scss'
import { PinAuthorizationProps } from '../types/Pin.types'

/**
 *  Prompts the user to enter their pin to authorize an API call,
 * for example to edit user information the user must enter their pin to
 * authorize the edits. If the user has not set a pin yet, they will be prompted
 * to do so, and then authorize the API call.
 */
const PinAuthorization = ({
  mainTitle,
  pageTitle,
  navigateTo,
  onClickAction,
  pinInputLabel,
  errorMessage,
  onSuccessfulPinSubmit,
  onFailedPinSubmit,
  hideMobileHeader,
  actionConfirmText,
  loadingType,
  modalTitle = 'PIN_SUBMITTING_MESSAGE',
  pin,
  setPin,
}: PinAuthorizationProps) => {
  const t = useTranslate()

  const {
    context: { user_details },
    send,
    currentState,
  } = useAccountService()

  const isLoading =
    currentState &&
    (currentState === loadingType || currentState === 'CREATING_NEW_PIN')

  const handleCreatePin = (newPin: string) => {
    send({
      type: 'CREATE_NEW_PIN',
      payload: {
        pin: newPin,
        successCallback: onSuccessfulPinSubmit,
        failureCallback: onFailedPinSubmit,
      },
    })
  }

  return (
    <Layout
      navigateTo={navigateTo}
      onClickAction={onClickAction}
      hideMobileHeader={hideMobileHeader}
      pageTitle={pageTitle}
      bottomSection={
        <NavigationButtons hideActionButton onClickFirst={onClickAction} />
      }
    >
      <ConfirmationModal
        animatedIcon={ANIMATION.loadingLightBlueDots}
        isOpen={isLoading}
        title={t(modalTitle)}
      />
      {!user_details?.pin_set ? (
        <PinConfirmation
          headerTitle={t('PIN_SETUP_TITLE')}
          handleSubmit={handleCreatePin}
        />
      ) : (
        <div className={style.pinInput}>
          {actionConfirmText && (
            <PageContent
              icon={ASSET.infoCircle}
              mainContent={actionConfirmText}
            />
          )}
          {mainTitle && (
            <div className={style[`pinInput__title`]}>{mainTitle}</div>
          )}

          <PinInput values={pin} label={pinInputLabel} onChange={setPin}>
            {errorMessage && (
              <TextError position="relative" errorText={errorMessage} />
            )}
          </PinInput>
        </div>
      )}
    </Layout>
  )
}

export default PinAuthorization
