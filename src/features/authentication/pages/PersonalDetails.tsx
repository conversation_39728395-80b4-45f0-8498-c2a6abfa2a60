import { useState } from 'react'
import ErrorBoundaryAndSuspense from '../../../common/components/ErrorBoundaryAndSuspense'
import Layout from '../../../common/components/Layout'
import NavigationButtons from '../../../common/components/NavigationButtons'
import TextError from '../../../common/components/TextError'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { PRIVATE } from '../../../routes/Route'
import DiscardChangesModal from '../components/DiscardChangesModal'
import EditNavigationButtons from '../components/EditNavigationButtons'
import IDVerificationCard from '../components/IDVerificationCard'
import PersonalDetailsForm from '../components/PersonalDetailsForm'
import VerificationFeedback from '../components/VerificationFeedback'
import { useAccountService } from '../hooks/useAccountService'
import { useBiometricsControl } from '../hooks/useBiometricsControl'
import { useEditPersonalDetails } from '../hooks/useEditPersonalDetails'
import FaceScan from './FaceScan'

const PersonalDetails = () => {
  const t = useTranslate()
  const { isMobileOrTablet } = useDeviceScreen()
  const {
    context: { user_details },
  } = useAccountService()

  const [showDiscardModal, setShowDiscardModal] = useState(false)
  const [showFaceScan, setShowFaceScan] = useState(false)
  const [showVerificationFeedback, setShowVerificationFeedback] =
    useState(false)

  const {
    userIsTyping,
    editApiError,
    saveEditedDetails,
    loading,
    inputValidation,
    ...rest
  } = useEditPersonalDetails()

  const { safeToStartIDV, isReadOnly } = useBiometricsControl({
    userIsTyping,
    userIsTypingCallback: () => setShowDiscardModal(true),
    setOpenFaceScanModal: setShowFaceScan,
  })

  const statusHandlers = {
    approved: undefined,
    not_reviewed: () => setShowVerificationFeedback(true),
    rejected: () => setShowVerificationFeedback(true),
  }

  const handleCardClick = user_details?.id_review_status
    ? statusHandlers[user_details?.id_review_status]
    : () => safeToStartIDV()

  return (
    <main>
      <DiscardChangesModal
        isOpen={showDiscardModal}
        onClose={() => setShowDiscardModal(false)}
        onSave={() => {
          saveEditedDetails(() => {
            setShowDiscardModal(false)
            setShowFaceScan(true)
          })
        }}
      />
      {showFaceScan && (
        <FaceScan
          asModal
          onClickExitScan={() => setShowFaceScan(false)}
          onSuccessfulScan={() => setShowFaceScan(false)}
          scanType="match-id"
        />
      )}

      <Layout
        pageTitle={t('PERSONAL_DETAILS.FORM_TITLE')}
        containerHeight={isMobileOrTablet ? 'lh' : 'sh'}
        navigateTo={showVerificationFeedback ? undefined : PRIVATE.ACCOUNT}
        onClickAction={
          showVerificationFeedback
            ? () => setShowVerificationFeedback(false)
            : undefined
        }
        containerMt="nomt"
        hideMobileHeader={showFaceScan}
        card={
          <IDVerificationCard
            status={user_details?.id_review_status}
            onClick={showVerificationFeedback ? undefined : handleCardClick}
          />
        }
        bottomSection={
          showVerificationFeedback ? (
            <NavigationButtons
              onClickFirst={() => setShowVerificationFeedback(false)}
              onClickSecond={() => setShowFaceScan(true)}
              secondButtonLabel={t('COMMON.TRY_AGAIN')}
            />
            // biome-ignore lint/nursery/noNestedTernary: <No better option>
          ) : !isReadOnly() ? (
            <EditNavigationButtons
              inputValidation={inputValidation}
              loading={loading}
              userIsTyping={userIsTyping}
              onSave={() => saveEditedDetails(() => setShowDiscardModal(false))}
            />
          ) : undefined
        }
      >
        <ErrorBoundaryAndSuspense>
          {showVerificationFeedback ? (
            <VerificationFeedback
              rejectionReason={user_details?.id_rejection_reason}
              verificationStatus={user_details?.id_review_status}
            />
          ) : (
            <PersonalDetailsForm
              {...rest}
              inputValidation={inputValidation}
              userIsTyping={userIsTyping}
              isReadOnly={isReadOnly()}
              navigateToBiometrics={() => setShowFaceScan(true)}
              saveEditedDetails={saveEditedDetails}
            />
          )}
        </ErrorBoundaryAndSuspense>

        {editApiError && <TextError errorText={editApiError} />}
      </Layout>
    </main>
  )
}

export default PersonalDetails
