import { useCallback, useRef } from 'react'
import { envs } from '../../../config/envs'
import { generateStatesObject } from '../../StateUtils'
import { AccountServiceContext } from '../AccountServiceContext'
import { authMachine } from '../AuthMachine'
import {
  AuthMachineEvent,
  StateType,
  States,
} from '../types/AuthMachineTypes.type'
const { environment } = envs

const { useSelector, useActorRef } = AccountServiceContext

/**
 * Consumes the AccountServiceProvider (subscribed to all states)
 *
 * An `AbortController` is automatically sent with every event, and the request
 * is aborted in the `finally` block of some services.
 *
 * Provided functionalities:
 * 1. `send` sends an event to the auth machine
 * 2. `currentState` the state the auth machine is currently in
 * 3. `context` global data stored in the auth machine
 * 4. `states` all the possible states that the auth machine can be in
 */
export const useAccountService = () => {
  const abortController = useRef<AbortController>(new AbortController())

  const actorRef = useActorRef()

  // Using the auth machine and subscribing to the state
  const { context, currentState, isAuthenticated, snapshot } = useSelector(
    (snapshot) => {
      let currentState = null

      const AUTH_TOKEN: StateType = 'AUTH_TOKEN'

      // Flattens the nested state into just a state value
      if (typeof snapshot.value === 'object') {
        if (AUTH_TOKEN in snapshot.value) {
          currentState = snapshot.value?.['AUTH_TOKEN']?.['AUTHENTICATED']
        }
      }

      return {
        currentState: currentState ?? snapshot.value,
        isAuthenticated: snapshot.context.isAuthenticated,
        context: snapshot.context,
        snapshot,
      }
    }
  )
  //Send from xstate function wrapped in a callback
  //TODO: Remove callback in react19
  // callback is very important for request cancelling
  // atm there is no better way because xstate
  const callbackSend = useCallback(
    ({ type, payload }: AuthMachineEvent) => {
      actorRef.send({
        type,
        payload: {
          abortController: abortController.current,
          finallyCallback: () => {
            abortController.current = new AbortController()
          },
          ...payload,
        },
      })
    },

    // Actor ref should not change, it is a static reference!
    [actorRef]
  )

  // Only used in developer env to directly send events to the auth machine
  // for testing. Also used in Cypress test as a hack for now until real model
  // based tests are added for xstate
  if (environment === 'development') {
    //@ts-ignore
    window.sendDevEvent = actorRef.send.bind(actorRef)
  }

  return {
    send: callbackSend,
    currentState,
    context,
    isAuthenticated,
    states: generateStatesObject(authMachine.states) as States,
    snapshot,
  }
}
