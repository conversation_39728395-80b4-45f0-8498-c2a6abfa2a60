import {
  InvestmentStrategyId,
  SexType,
  TontinatorParamsMode,
} from '../../../common/types/CommonTypes.types'
import { AgeMonth } from '../../CommonState.type'
import {
  InvestmentDetails,
  UserDetails,
} from '../../authentication/types/AuthMachineTypes.type'

type DetectedCountry = {
  alpha3?: string
}

type FormatAmountParams = {
  amount: number | bigint
  currency?: string
  style?: 'percent' | 'currency'
  notation?: 'standard' | 'engineering' | 'compact' | 'scientific'
  digits?: {
    minimumFractionDigits?: number
    maximumFractionDigits?: number
    maximumSignificantDigits?: number
    minimumSignificantDigits?: number
  }
}

type FormatAmountReturn =
  | {
      formattedAmountWithSymbol: string
      symbol: string
      formattedAmount: string
    }
  | undefined

type TontinatorUIParams = {
  defaultRetirementAgeSlider?: AgeMonth
  defaultCurrentAgeSlider?: AgeMonth
  defaultOneTimeSliderValue?: number
  defaultMonthlySliderValue?: number
  defaultSex?: SexType
  oneTimeContribution?: number[]
  monthlyContribution?: number[]
  oneTimeContributionIfRetired?: number[]
  monthlyContributionMinIfOnly?: number
  oneTimeContributionMinIfOnly?: number
  minRetirementAge?: AgeMonth
  maxRetirementAge?: AgeMonth
  minCurrentAge?: AgeMonth
  maxCurrentAge?: AgeMonth
}

type SupportedCountry = {
  alpha3?: string
  supportedInvestments?: InvestmentDetails
  tontinatorParams?: TontinatorUIParams
}

type BankContext = {
  bankingInfo?: {
    nextPayout?: Array<{
      gross?: {
        amount?: number
        currency?: string
      }
    }>
    payinHistory?: Array<{
      nominalBalance: {
        amount: number
        currency: string
      }
    }>
  }
  bankingInfoError?: unknown
}

type DefaultParams = {
  contributionAge?: AgeMonth
  retirementAge?: AgeMonth
  sex?: SexType
  oneTimeContribution?: number
  countryOfResidence?: string
  monthlyContribution?: number
  strategy?: InvestmentStrategyId
  paramsMode?: TontinatorParamsMode
}

type NextPayoutCardProps = {
  className?: string
}

type FundedDashboardProps = {
  data?: DefaultParams
  error?: null
  isLoading?: boolean
}

type MobileAppBarProps = {
  completedKyc?: boolean
}

type NavigationCardProps = {
  headerImage?: string
  subTitle?: string
  title?: string
  navigateTo?: string
  showArrow?: boolean
  variant?: string
}

type HeaderProps = {
  className?: string
  title?: string
}

type PageLayoutProps = {
  containerHeight?: string
  containerMt?: string
  children?: React.ReactNode
}

export type {
  BankContext,
  DefaultParams,
  DetectedCountry,
  FormatAmountParams,
  FormatAmountReturn,
  FundedDashboardProps,
  HeaderProps,
  InvestmentDetails,
  MobileAppBarProps,
  NavigationCardProps,
  NextPayoutCardProps,
  PageLayoutProps,
  SupportedCountry,
  TontinatorUIParams,
  UserDetails,
}
