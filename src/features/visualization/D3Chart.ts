import {
  type Axis,
  type Line,
  type NumberValue,
  area,
  axisBottom,
  axisRight,
  bisector,
  extent,
  line,
  pointer,
  scaleLinear,
  scaleTime,
  select,
  selectAll,
} from 'd3'
import { generateUniqueId } from '../../common/utils/UtilFunctions'
import { Styling } from './Styling'
import {
  AddAnnotationParams,
  CrosshairAndAnnotationParams,
  DrawAxisX,
  DrawCircleOnLineParams,
  DrawGraphLineAndAreaParams,
  GenerateFocusCirclesParams,
  LinearScale,
  MoveAxisTicks,
  RenderAreaLineParams,
  ScaleParams,
  StylingOptions,
  SvgSelection,
  TickFormatter,
} from './types/D3Chart.types'
import { TextBoundaryBox } from './types/Utils.types'
import {
  ForecastPayout,
  LegendItem,
  StylingOptionsD3,
} from './types/Visualization.types'
import {
  adjustHoveringLineBox,
  amountSymbols,
  centerText,
  dateParser,
  getObjectPropertyValue,
  hoveringCalloutDistanceFromCrosshair,
  preventSameLineTypeOverlap,
} from './utils/D3DataUtils'
import {
  createTip,
  fitCalloutToBoundaries,
  generateComparisonLine,
  generateFocusCircle,
  generateRectangleWithText,
} from './utils/D3SvgUtils'

/**
 * Renders an SVG container which serves as a base for the chart,
 * where all other svg elements are appended to
 */
const drawChartSvg = ({
  selector,
  height,
  width,
}: {
  selector: string
  height: number
  width: number
}) =>
  select(selector)
    .attr('height', height)
    .attr('width', width)
    .style('background', 'white')
    .style('overflow', 'visible')
    .style('cursor', 'pointer')

/**
 * Moves the X axis ticks by Y scale
 */
const moveXticks = ({ chartSvg, pixels, axisSelector }: MoveAxisTicks) => {
  chartSvg
    .selectAll(axisSelector)
    .selectAll('text')
    .attr('transform', `translate(0, ${pixels})`)
}

/**
 * Moves the Y axis ticks by Y scale
 */
const moveYticks = ({ chartSvg, pixels, axisSelector }: MoveAxisTicks) => {
  chartSvg
    .selectAll(axisSelector)
    .selectAll('text')
    .attr('transform', `translate(${pixels}, 0)`)
}

/**
 * Generates an X scale using the D3 `scaleTime` function. The `scaleTime`
 * function only operates on JavaScript `Date` objects
 */
const generateTimeScaleX = <T>({
  data,
  maxRangeValue,
  mapData,
}: ScaleParams<T, Date>) =>
  scaleTime()
    //domain is the complete set of values extent returns the minimal and
    //maximum value in an array
    .domain(extent(data, mapData) as [Date, Date])
    //range typically used to transform (or ‘map’) data values into visual
    //variables (such as position, length and color).
    .range([0, maxRangeValue])

/**
 * Generates an X scale using the D3 `scaleLinear` function.
 */
const generateLinearScaleX = <T>({
  data,
  maxRangeValue,
  mapData,
}: ScaleParams<T, number>) =>
  scaleLinear()
    //domain is the complete set of values extent returns the minimal and
    //maximum value in an array
    .domain(extent(data, mapData) as [number, number])
    //range typically used to transform (or ‘map’) data values into visual
    //variables (such as position, length and color).
    .range([0, maxRangeValue])

/**
 * Generates an Y scale using the D3 `scaleLinear` function.Creates a scale with
 * a linear relationship between input and output.
 */
const generateScaleY = ({
  domainMax,
  rangeMax,
}: {
  domainMax: number
  rangeMax: number
}) =>
  //the scale functions in d3 return data points that are used by d3 to draw
  //chart elements like axis, lines, areas so on..
  scaleLinear()
    //domain is the complete set of values extent returns the minimal and
    //maximum value in an array this only finds for one object!
    .domain([0, domainMax])
    .nice()
    //range is the set of resulting values of a function
    .range([rangeMax, 0])

/**
 * Draws an X axis at the bottom of the graph, using the `axisBottom` D3
 * function
 */
const drawAxisX = <T>({
  xScale,
  height,
  numberOfTicks,
  tickFormatter,
  data,
  dataIterator,
}: DrawAxisX<T>) =>
  axisBottom(xScale)
    //specifies how many ticks should the graph have
    .ticks(numberOfTicks)
    .tickValues(data?.map(dataIterator))
    //formats ticks values
    .tickFormat(tickFormatter)
    //This reduces the tick size so it does not go out of bounds the grid lines
    //to be exact This is actually a grid line
    .tickSizeInner(-height)
    //removes last tick so the axis lines don't cross over we want a nice square
    .tickSizeOuter(0)

/**
 *  Draws an Y axis on the left side using thr `axisLeft` D3 function
 */
const drawAxisY = ({
  yScale,
  width,
  numberOfTicks,
  tickFormatter,
}: {
  yScale: LinearScale
  width: number
  numberOfTicks: number
  tickFormatter: TickFormatter
}) =>
  axisRight(yScale)
    .ticks(numberOfTicks)
    .tickFormat(tickFormatter)
    .tickSizeInner(-width)
    //removes last tick so the axis lines don't cross over we want a nice square
    .tickSizeOuter(0)

/**
 * Draws a chart line from `xScale`, `yScale` using the D3 `line` function
 */
const drawChartLine = <T>(
  xScale: (param: T) => number,
  yScale: (param: T) => number
) =>
  line()
    //x expects function returned from scaling X
    .x(xScale as () => number)
    //y expects function returned from scaling Y
    .y(yScale as () => number)

/**
 * Draws a area from `xScale`, lower and upper bounds needs to be specified by
 * using the `yScale` function, otherwise the areas will overlap and cause
 * colors to look bad if they have lowered opacity values
 */
const drawArea = <T>(
  xScale: (param: T) => number,
  y0Scale: (param: T) => number,
  y1Scale: ((param: T) => number) | number
) =>
  area()
    .x(xScale as () => number)
    .y0(y0Scale as () => number)
    .y1(y1Scale as () => number)

/**
 * Appends the X Axis to the graph svg
 */
const appendAxisXtoGraph = ({
  chartSvg,
  xAxis,
  xAxisClass,
  height,
}: {
  chartSvg: SvgSelection
  xAxis: Axis<NumberValue>
  xAxisClass: string
  height: number
}) => {
  chartSvg.select(`.${xAxisClass}`).remove()

  return (
    chartSvg
      .append('g')
      .attr('class', xAxisClass)
      //call Invokes the specified function exactly once, passing in this
      //selection along with any optional arguments. Returns this selection.
      //This is equivalent to invoking the function by hand but facilitates
      //method chaining.
      .call(xAxis)
      .attr('transform', `translate(0,${height})`)
      //selects all the ticks in xAxis and breaks the words to it is rendered 1
      //word per line
      .selectAll(`.${xAxisClass} .tick text`)
  )
}

/**
 * Appends the X Axis to the graph svg
 */
const appendAxisYtoGraph = ({
  chartSvg,
  yAxis,
  yAxisClass,
  width,
}: {
  chartSvg: SvgSelection
  yAxis: Axis<NumberValue>
  yAxisClass: string
  width: number
}) => {
  chartSvg.select(`.${yAxisClass}`).remove()

  return chartSvg
    .append('g')
    .attr('class', yAxisClass)
    .attr('transform', `translate(${width},0)`)
    .call(yAxis)
}

/**
 * Renders a chart line for the passed in data. Styling options are also
 * provided for the line
 */
const renderChartLine = <T>({
  chartSvg,
  id,
  data,
  drawLine,
  strokeWidth,
  strokeColor,
  fill,
  strokeDashArray = '0, 0',
  arrowHead = false,
  transitionDuration = 1_500,
}: {
  chartSvg: SvgSelection
  id: string
  data: Array<T>
  drawLine: Line<T>
  strokeWidth: string
  strokeColor: string
  fill: string
  strokeDashArray?: string
  arrowHead?: boolean
  transitionDuration?: number
}) => {
  //Remove previous lines if there is a data update
  chartSvg.select(`#${id}`).remove()
  //Removes previous arrows if there were any
  chartSvg.select(`#${id}_arrow`).remove()

  //Adds an arrow head to the end of the line
  if (arrowHead) {
    chartSvg
      .append('svg:marker')
      .attr('id', `${id}_arrow`)
      .attr('orient', 'auto')
      .attr('markerUnits', 'userSpaceOnUse')
      .attr('markerWidth', 30)
      .attr('markerHeight', 30)
      .attr('refX', 3.5)
      .attr('refY', 2)
      .attr('viewBox', '-3 -3 10 10')
      .append('path')
      .attr('d', 'M0,0 V4 L4,2 Z')
      .style('fill', 'transparent')
      .transition()
      .duration(transitionDuration + 3)
      .style('fill', strokeColor)
  }

  const path = chartSvg
    .append('g')
    .attr('id', id)
    .selectAll('.line')
    .data([data])
    .join('path')
    .attr('d', (d) => drawLine(d))
    .attr('fill', fill)
    .attr('stroke-width', strokeWidth)
    .attr('stroke', strokeColor)
    .attr('stroke-dasharray', strokeDashArray)

  const totalLength = (path?.node() as SVGPathElement)?.getTotalLength() ?? 0

  path
    .attr('stroke-dasharray', `${totalLength} ${totalLength}`)
    .attr('stroke-dashoffset', totalLength)
    // elements should already be appended to the svg before a transition
    // otherwise they won't render on certain browsers
    .attr('marker-end', `url(#${id}_arrow)`)
    .transition()
    .duration(transitionDuration)
    .attr('stroke-dashoffset', 0)
}

/**
 * Generates annotations that are only visible when the user is dragging or
 * hovering  on the chart
 */
const generateHoveringAnnotations = <T>({
  styling,
  depositAccountStyling,
  annuityStyling,
  hidden,
  chartSvg,
  data,
  mainSVGContainerId,
}: {
  hidden: boolean
  chartSvg: SvgSelection
  data: Array<T>
  mainSVGContainerId: string
} & StylingOptions) => {
  //Order of the elements created matters, for example since the annotation
  //elements are created after the focus ball elements that means that the
  //annotation element will be on top of the focus ball "z-index"
  const tontineGroupAnnotations = data?.map((_, tontineLineIndex) =>
    generateRectangleWithText({
      mainSVGContainerId,
      chartSvg,
      id: generateUniqueId(),
      text: '',
      color: styling?.[tontineLineIndex]?.color ?? 'black',
      textColor: styling?.[tontineLineIndex]?.textColor ?? '',
      hidden,
    })
  )

  const {
    annotationGroup: depositAccountAnnotation,
    textSvg: depositAccountText,
  } = generateRectangleWithText({
    mainSVGContainerId,
    chartSvg,
    id: generateUniqueId(),
    text: '',
    color: depositAccountStyling?.color,
    textColor: depositAccountStyling?.textColor ?? '',
    hidden,
  })

  const { annotationGroup: annuityAnnotation, textSvg: annuityText } =
    generateRectangleWithText({
      mainSVGContainerId,
      chartSvg,
      id: generateUniqueId(),
      text: '',
      color: annuityStyling?.color,
      textColor: annuityStyling?.textColor ?? '',
      hidden,
    })

  return {
    depositAccountAnnotation,
    depositAccountText,
    annuityAnnotation,
    annuityText,
    tontineGroupAnnotations,
  }
}

/**
 * Generates focus circles on a certain line, for now only tontine, annuity and
 * deposit accounts are supported
 */
const generateFocusCircles = <T>({
  chartSvg,
  tontineFocusPathID,
  depositAccountFocusPathID,
  annuityFocusPathID,
  hidden,
  styling,
  depositAccountStyling,
  annuityStyling,
  focusCirclesRadius,
  focusGroupID = 'focus-group-circles-tontinator',
  data,
}: GenerateFocusCirclesParams<T>) => {
  if (select(`#${focusGroupID}`)) {
    select(`#${focusGroupID}`)?.remove()
  }

  const focusBallGroup = chartSvg.append('g').attr('id', focusGroupID)

  const tontineLineFocusCircles = data?.map((_, tontineLineIndex) => {
    return generateFocusCircle({
      chartSvg: focusBallGroup,
      id: tontineFocusPathID + tontineLineIndex,
      styling: styling?.[tontineLineIndex] ?? ({} as StylingOptionsD3),
      radius: focusCirclesRadius,
      hidden,
    })
  })

  const focusBallAnnuity =
    annuityStyling?.color === Styling.hidden
      ? null
      : generateFocusCircle({
          chartSvg: focusBallGroup,
          id: annuityFocusPathID,
          styling: annuityStyling,
          radius: focusCirclesRadius,
          hidden,
        })

  const focusBallDepositAccount =
    depositAccountStyling?.color === Styling.hidden
      ? null
      : generateFocusCircle({
          chartSvg: focusBallGroup,
          id: depositAccountFocusPathID,
          styling: depositAccountStyling,
          radius: focusCirclesRadius,
          hidden,
        })

  return {
    focusBallDepositAccount,
    focusBallAnnuity,
    tontineLineFocusCircles,
  }
}

/**
 * Value **rounding** is done with `Math.round` before returning the value
 */
const crosshairAndAnnotation = <T extends ForecastPayout>({
  chartSvg,
  data,
  xScale,
  yScale,
  options,
  focusPathID,
  styling,
  xDataToScale,
  yTontineData,
  yAnnuityData,
  yDepositAccountData,
  bisectorDataFunction,
  formatter,
  annuityFocusPathID,
  depositAccountFocusPathID,
  annuityStyling,
  depositAccountStyling,
  height,
  width,
  toggles,
  hideFocusCircles,
  hideCrosshair,
  hideAnnotations,
  focusBallGroupID,
  mainSVGContainerId,
  onHoverOrTapStart,
  onHoverOrTapLeave,
}: CrosshairAndAnnotationParams<T>) => {
  //First age tick from all lines
  const xAxisFirstAgeTick = Math.min(
    //Always take the first item from the array because the first item contains
    //the income start age
    ...data.map((payoutLine) => payoutLine[0]?.age?.years)
  )
  const xAxisLastAgeTick = 100
  const centerTextIfAbove3Digits = 14
  const centerTextPx = 9
  const focusCirclesRadius = 6
  const yDistanceFromLineInPx = -25
  const additionalHeightForComparisonBg = 10
  const alignLeftAgeThreshold = 91
  const snapToTontineLineAgeThreshold = 91
  const tontineSnapPixels = 30
  const pixelsAboveAnnuityLine = 55
  const noModify = 0
  const tontine12moPercent = 'tontine.12mo_percent'
  const depositAccount12moPercent = 'deposit_account.12mo_percent'
  const annuity12moPercent = 'annuity.12mo_percent'
  const maxPercentThresholdForSameTypeLine = 30
  const hoverLineYAdjustment = {
    lowestLine: -20,
    highestLine: 10,
  }
  const xInitialCalloutDistance = 25
  const calloutStyling = Styling.callouts

  // Generate a hover line with styling The line needs to be rendered first,
  //otherwise it will be on top of the annotations and won't look good
  const {
    comparisonLine: crossHair,
    comparisonLineComparisonBox,
    comparisonLineComparisonText,
  } = generateComparisonLine({
    chartSvg,
    mainSVGContainerId,
    x1: 0,
    x2: 0,
    y1: height + additionalHeightForComparisonBg,
    y2: 0,
    hidden: true,
  })

  const { focusBallDepositAccount, focusBallAnnuity, tontineLineFocusCircles } =
    generateFocusCircles({
      data,
      chartSvg,
      tontineFocusPathID: focusPathID,
      depositAccountFocusPathID,
      annuityFocusPathID,
      hidden: true,
      styling,
      depositAccountStyling,
      annuityStyling,
      focusCirclesRadius,
      focusGroupID: `${focusBallGroupID}${mainSVGContainerId}`,
    })

  const {
    depositAccountAnnotation,
    depositAccountText,
    annuityAnnotation,
    annuityText,
    tontineGroupAnnotations,
  } = generateHoveringAnnotations({
    mainSVGContainerId,
    chartSvg,
    styling: styling as Array<StylingOptionsD3>,
    depositAccountStyling,
    annuityStyling,
    hidden: true,
    data,
  })

  //Creates a bisected function for x axis data
  const bisectorResult = bisector(bisectorDataFunction)
  const bisectorGen = bisectorResult?.left.bind(bisectorResult)
  //Lifting the tontine annotations from the `handleOnMouseMove` in order to
  //clean them up after the mouse no longer moves on the chart
  let tontineLineAnnotations: typeof tontineGroupAnnotations = []
  let tontineFocusCircles: typeof tontineLineFocusCircles = []
  //Need to keep the previous closes data for comparison purposes
  let previousClosestData = {} as ForecastPayout

  /**
   *
   * Handles moving the crosshair line to follow the mouse pointer
   */
  const handleCrossHairElement = <T extends ForecastPayout>({
    closestData,
    mousePointerPositionX,
  }: {
    closestData: T
    mousePointerPositionX: number
  }) => {
    const textToDisplay = Math.min(
      //Avoids rendering NaN if the previous closes data is no existent when the
      //chart is re-rendering and the mouse pointer is on the chart
      previousClosestData?.age?.years ?? 9999,
      closestData?.age?.years
    )

    //Controls the hovering vertical line position, by passing in mouse pointer
    //coordinates
    crossHair
      .attr('x1', mousePointerPositionX)
      .attr('x2', mousePointerPositionX)
      .style('visibility', hideCrosshair ? 'hidden' : 'visible')

    //Makes sure that the comparison box follows the mouse position
    comparisonLineComparisonBox
      .attr(
        'cx',
        adjustHoveringLineBox({
          mousePointerPositionX,
          xAxisFirstAgeTick,
          xAxisLastAgeTick,
          currentAgeData: textToDisplay,
        })
      )
      .style('visibility', hideCrosshair ? 'hidden' : 'visible')

    //Makes sure that the text follows the comparisonLineComparisonBox
    comparisonLineComparisonText
      .attr(
        'x',
        adjustHoveringLineBox({
          mousePointerPositionX: centerText({
            mousePointerPositionX,
            ageOnLine: textToDisplay,
            adjustByIfAbove3Digits: centerTextIfAbove3Digits,
            adjustBy: centerTextPx,
            xAxisLastAgeTick,
          }),
          xAxisFirstAgeTick,
          xAxisLastAgeTick,
          currentAgeData: textToDisplay,
        })
      )
      .text(textToDisplay)
      .style('visibility', hideCrosshair ? 'hidden' : 'visible')
  }

  /**
   * Handles moving the annuity focus circle and callout to follow the mouse
   * pointer
   */
  const handleAnnuityElements = <T extends ForecastPayout>({
    closestData,
    xPositionForBall,
    xCalloutDistance,
  }: {
    closestData: T
    xPositionForBall: number
    xCalloutDistance: number
  }) => {
    const positionY = yScale(
      getObjectPropertyValue(closestData, yAnnuityData) as number
    )

    //Adds the annotation to be on top of the annuity line because the annuity
    //line is very close to X axis in inflation state
    const topWhenInflation =
      (getObjectPropertyValue(closestData, yDepositAccountData) as number) <= 0
        ? noModify
        : pixelsAboveAnnuityLine

    // Needed to update the text with the newest value when user interacts with
    //the line chart
    const annuityAmountToRender = getObjectPropertyValue(
      closestData,
      toggles?.percent ? annuity12moPercent : yAnnuityData
    ) as number

    if (annuityAmountToRender) {
      annuityText
        .text(
          amountSymbols({
            amount: annuityAmountToRender,
            formatter: formatter,
          })
        )
        .style('top', function () {
          const bBox = (this as SVGTextElement).getBBox()

          annuityAnnotation
            .select('rect')
            .attr('height', bBox.height + 2)
            .attr('width', bBox.width + 2 * calloutStyling.xPadding)
            .attr('x', bBox?.x - calloutStyling.xPadding)
            .attr('y', bBox?.y - calloutStyling.yPadding)
          // Updates the distance so when the bounding box expands, it does not
          // visually move away from the hovering line
          xCalloutDistance = hoveringCalloutDistanceFromCrosshair(bBox.width)

          return ''
        })
    }

    //Controls the focus ball position for annuities
    focusBallAnnuity
      ?.attr('cx', xPositionForBall)
      .attr('cy', positionY)
      .style('visibility', hideFocusCircles ? 'hidden' : 'visible')

    const xDistanceFrom =
      closestData?.age?.years > alignLeftAgeThreshold
        ? xPositionForBall - xCalloutDistance
        : xPositionForBall + xCalloutDistance

    annuityAnnotation
      .style(
        'transform',
        `translate(${xDistanceFrom}px, ${
          positionY + yDistanceFromLineInPx + topWhenInflation
        }px)`
      )
      .style('visibility', hideAnnotations ? 'hidden' : 'visible')
  }

  /**
   * Handles moving the deposit line focus circle and callout to follow the
   * mouse pointer
   */
  const handleDepositLineElements = <T extends ForecastPayout>({
    closestData,
    xPositionForBall,
    xCalloutDistance,
  }: {
    closestData: T
    xPositionForBall: number
    xCalloutDistance: number
  }) => {
    const yData = getObjectPropertyValue(
      closestData,
      yDepositAccountData
    ) as number
    //Calculates the Y position by scaling the Y axis amount data
    const positionY = yScale(yData)
    //Hides the deposit account annotation when the deposit account amount is
    //exhausted
    const isHiddenWhen0 = yData <= 0

    //Controls the focus ball position for deposit account
    focusBallDepositAccount
      ?.attr('cx', xPositionForBall)
      .attr('cy', positionY)
      .style(
        'visibility',
        hideFocusCircles || isHiddenWhen0 ? 'hidden' : 'visible'
      )

    const depositAccountAmountToRender = getObjectPropertyValue(
      closestData,
      toggles?.percent ? depositAccount12moPercent : yDepositAccountData
    ) as number

    if (depositAccountAmountToRender) {
      depositAccountText
        .text(
          amountSymbols({
            amount: depositAccountAmountToRender,
            formatter: formatter,
          })
        )
        .style('top', function () {
          const bBox = (this as SVGTextElement).getBBox()
          depositAccountAnnotation
            .select('rect')
            .attr('height', bBox.height + 2)
            .attr('width', bBox.width + 2 * calloutStyling.xPadding)
            .attr('x', bBox?.x - calloutStyling.xPadding)
            .attr('y', bBox?.y - calloutStyling.yPadding)

          // Updates the distance so when the bounding box expands, it does not
          // visually move away from the hovering line
          xCalloutDistance = hoveringCalloutDistanceFromCrosshair(bBox.width)

          return ''
        })
    }

    const xDistanceFrom =
      closestData?.age?.years > alignLeftAgeThreshold
        ? xPositionForBall - xCalloutDistance
        : xPositionForBall + xCalloutDistance

    depositAccountAnnotation
      .style(
        'transform',
        `translate(${xDistanceFrom}px, ${positionY + yDistanceFromLineInPx}px)`
      )
      .style(
        'visibility',
        hideAnnotations || isHiddenWhen0 ? 'hidden' : 'visible'
      )
  }

  const handleTontineElements = ({
    data,
    x0,
    mousePointerPositionX,
    xCalloutDistance,
  }: {
    data: Array<Array<T>>
    x0: Date
    mousePointerPositionX: number
    xCalloutDistance: number
  }) => {
    //Needed to have these top level in order for annuity and deposit account
    //have access to these values
    let closestData = {} as ForecastPayout
    let xPositionForBall = 0
    //By default it is right side aligned as soon as the threshold is reached it
    //aligns the annotations left side
    let xDistanceFrom = 0

    //Forecast response data containing one or multiple lines of the same and
    //different type to draw
    data.forEach((line, payoutLineIndex) => {
      if (yTontineData) {
        const indexOfDataPoint = bisectorGen(line, x0)
        //Returns a data point where the mouse x position
        closestData = line[indexOfDataPoint]
        //Calculates the Y position by scaling the Y axis amount data
        const positionYTontine = yScale(
          getObjectPropertyValue(closestData, yTontineData) as number
        )
        //Calculates the position the X position for the focus circle
        xPositionForBall = xScale(
          dateParser(
            getObjectPropertyValue(closestData, xDataToScale) as string
          )
        )

        //Snaps the tontine annotation to the tontine line, in order for the
        //annotation not to go outside the charts height
        const snapBackToLine =
          //0 means don't modify
          closestData?.age?.years > snapToTontineLineAgeThreshold &&
          !toggles?.inflation
            ? noModify
            : tontineSnapPixels

        //Line and focus circles array of elements
        tontineLineAnnotations = tontineGroupAnnotations
        tontineFocusCircles = tontineLineFocusCircles

        //Multiple annotations of the same type are generated
        const { annotationGroup, textSvg } =
          tontineLineAnnotations[payoutLineIndex]

        //Prevents an overlap if the two lines of the same type, by using the
        //current payout amount the cursor was on. Because we compare payout
        //amount on that month, is they amount has small difference in
        //percentage, then overlap handle function handles the overlap
        const preventOverlap =
          //More than 1 line, use algorithm
          data?.length > 1
            ? preventSameLineTypeOverlap({
                previousClosestData,
                closestData,
                thresholdPercentToCompareBy: maxPercentThresholdForSameTypeLine,
                adjustYForHighestPayoutLine: hoverLineYAdjustment.highestLine,
                adjustYForLowestPayoutLine: hoverLineYAdjustment.lowestLine,
                xAxisLastAgeTick,
              })
            : noModify

        if (closestData) {
          const tontineAmountToRender = getObjectPropertyValue(
            closestData,
            toggles?.percent ? tontine12moPercent : yTontineData
          ) as number

          if (tontineAmountToRender) {
            textSvg
              .text(
                amountSymbols({
                  amount: tontineAmountToRender,
                  formatter,
                })
              )
              .style('top', function () {
                const bBox = (this as SVGTextElement).getBBox()
                //Gets the bounding box so if the annotation text is longer than
                //the bounding box
                annotationGroup
                  .select('rect')
                  .attr('height', bBox.height + 2)
                  .attr('width', bBox.width + 2 * calloutStyling.xPadding)
                  .attr('x', bBox?.x - calloutStyling.xPadding)
                  .attr('y', bBox?.y - calloutStyling.yPadding)

                xCalloutDistance = hoveringCalloutDistanceFromCrosshair(
                  bBox.width
                )

                return ''
              })
          }

          xDistanceFrom =
            closestData?.age?.years > alignLeftAgeThreshold
              ? xPositionForBall - xCalloutDistance
              : xPositionForBall + xCalloutDistance

          annotationGroup
            .style(
              'transform',
              `translate(${xDistanceFrom}px, ${
                positionYTontine +
                yDistanceFromLineInPx -
                snapBackToLine +
                preventOverlap
              }px)`
            )
            .style('visibility', hideAnnotations ? 'hidden' : 'visible')

          //Multiple focus circles of the same type are generated and controlled
          //on the chart
          tontineFocusCircles[payoutLineIndex]
            .attr('cx', xPositionForBall)
            .attr('cy', positionYTontine)
            .style('visibility', hideFocusCircles ? 'hidden' : 'visible')
        }
      }

      //Makes sure that the hover line does not get out of bounds of the chart
      //container
      if (mousePointerPositionX > 0 && mousePointerPositionX < width) {
        handleCrossHairElement({
          closestData,
          mousePointerPositionX,
        })
      }

      //Take the previous data so comparison can be made on what to render
      previousClosestData = closestData
    })

    return { xPositionForBall, xDistanceFrom, closestData }
  }

  //GETS UPDATED EVERY MOUSE/FINGER DRAG MOVE! Mouse or ontouch event handler
  const handleOnMouseMove = (event: TouchEvent) => {
    //Prevents the user from triggering other touch events, for example if they
    //swipe left on a mobile browser they might trigger an unwanted action while
    //interacting with the line chart.
    event.preventDefault()
    //Takes only the X axis mouse position
    const mousePointerPositionX = pointer(
      event?.touches && event?.touches?.length > 0 ? event?.touches[0] : event
    )[0]
    //Inverts the data using the mouse pointer position to be used to retrieve
    //the index of the point position
    const x0 = xScale?.invert(mousePointerPositionX)

    ///////////MULTIPLE TONTINE LINES HANDLING STARTS HERE /////////////////
    //Forecast response data containing one or multiple lines of the same and
    //different type to draw
    const { xPositionForBall, closestData } = handleTontineElements({
      data,
      x0,
      mousePointerPositionX,
      xCalloutDistance: xInitialCalloutDistance,
    })

    //Makes sure to only render the annotation if there is closest data
    if (options?.showFocusCircleOnPath && closestData) {
      if (yDepositAccountData && focusBallDepositAccount) {
        handleDepositLineElements({
          closestData,
          xPositionForBall,
          xCalloutDistance: xInitialCalloutDistance,
        })
      }

      if (yAnnuityData && focusBallAnnuity) {
        handleAnnuityElements({
          closestData,
          xPositionForBall,
          xCalloutDistance: xInitialCalloutDistance,
        })
      }
    }
    // 10ms is good enough for user to have smooth 60fps experience and reduces
    // the unnecessary calls TODO: Check throttle for performance
  }

  /**
   * Clean up event after the mouse/tap is no longer interacting with the line
   * chart
   */
  const handleOnMouseLeave = () => {
    onHoverOrTapLeave?.()
    //Hides same line type annotations
    tontineLineAnnotations.forEach(({ annotationGroup }, index) => {
      annotationGroup.style('visibility', 'hidden')
      tontineFocusCircles[index].style('visibility', 'hidden')
    })

    if (focusBallAnnuity) {
      focusBallAnnuity.style('visibility', 'hidden')
    }
    if (focusBallDepositAccount) {
      focusBallDepositAccount.style('visibility', 'hidden')
    }
    annuityAnnotation.style('visibility', 'hidden')
    depositAccountAnnotation.style('visibility', 'hidden')
    crossHair.style('visibility', 'hidden')
    comparisonLineComparisonBox.style('visibility', 'hidden')
    comparisonLineComparisonText.style('visibility', 'hidden')
    //Show all annotations on leave
    selectAll(`.annotation${mainSVGContainerId}`).style('visibility', 'visible')
  }

  //At least one option needs to be passed to attach event listeners, if no
  //options is passed in there is no point of adding an event listener TODO:
  //This does not make sense and it is not going to scale well, not all charts
  //will have focus circle or a hovering mouse annotation
  if (options?.showFocusCircleOnPath || options?.showHoveringMouseAnnotation) {
    chartSvg
      .on('mousemove touchmove', handleOnMouseMove)
      .on('mouseleave touchend', handleOnMouseLeave)
      //Hide all annotations on mouse enter
      .on('mouseenter touchstart', (event: TouchEvent & MouseEvent) => {
        selectAll(`.annotation${mainSVGContainerId}`).style(
          'visibility',
          'hidden'
        )
        handleOnMouseMove(event)
        onHoverOrTapStart?.(event)
      })
  }
}

/**
 * Removes a randomly added `path` from a specified parent CSS selector
 */
const removeGarbagePath = (selector: string) =>
  select(selector).select('path').remove()

/**
 * Clean up is done externally! This functions does NOT include cleaning up
 * duplicate or stale render elements! Adds an annotation with text above the
 * chart line
 */
const addAnnotation = ({
  chartSvg,
  color,
  xScale,
  yScale,
  text,
  id,
  textColor,
  transitionDuration,
  firstAnnotation = false,
  //Need to add default value here otherwise TS will not transpile this
  lastAnnotation = false,
  tipOnTop = false,
  mainSVGContainerId,
}: AddAnnotationParams) => {
  let adjustAnnotationDistance = 0

  if (firstAnnotation) {
    adjustAnnotationDistance = fitCalloutToBoundaries(text) + 2
  }

  if (lastAnnotation) {
    adjustAnnotationDistance = -fitCalloutToBoundaries(text) + 2
  }

  let textBoundaryBox = {} as DOMRect

  //Responsible for the text position for Y axis
  const DY = 20

  const { xPadding, yPadding, tipWidth, tipHeight } = Styling.callouts

  //Creates an annotation group
  const annotationGroup = chartSvg
    .append('g')
    .attr('class', `annotation${mainSVGContainerId}`)
    .attr('id', `annotationId-${id}`)

  const [rectId, textId] = [`annotation-rect-${id}`, `annotation-text-${id}`]

  //Adds text to the annotation group
  annotationGroup
    .append('text')
    .attr('id', textId)
    .attr('class', 'annotation-text')
    .attr('text-anchor', Styling.textAnchor)
    .attr('x', xScale + adjustAnnotationDistance)
    .attr('y', yScale - DY - tipHeight)
    .text(text)
    .style('font-weight', Styling.fontWeight)
    .style('fill', textColor ?? Styling.textColor)
    .style('top', function () {
      //Gets the bounding box so if the annotation text is longer than the
      //bounding box
      textBoundaryBox = this.getBBox()
      return ''
    })

  //Adds a rect svg element to serve as a background for the annotation text
  annotationGroup
    .append('rect')
    .attr('id', rectId)
    .attr('class', 'back')
    .attr('rx', 4)
    .attr('x', textBoundaryBox?.x - xPadding)
    .attr('y', textBoundaryBox?.y - yPadding)
    .attr('width', textBoundaryBox?.width + 2 * xPadding)
    .attr('height', textBoundaryBox?.height + 2 * yPadding)
    .style('fill', color)

  const tipOnTopDistance = tipOnTop ? 26 : 0

  const xMid =
    textBoundaryBox?.x -
    xPadding +
    (textBoundaryBox?.width + 2 * xPadding) / 2 -
    adjustAnnotationDistance

  const yMax =
    textBoundaryBox?.y -
    yPadding +
    textBoundaryBox?.height +
    2 * yPadding -
    1 -
    tipOnTopDistance

  //Adds small annotation tips to the rectangle background
  annotationGroup
    .append('polygon')
    .attr(
      'points',
      createTip({
        tipLeftSide: firstAnnotation,
        xMid,
        yMax,
        tipWidth,
        tipHeight,
      })
    )
    .style('fill', color)
    .attr(
      'transform',
      `${tipOnTop ? `rotate(180,${xMid},${yMax + 2.9})` : 'rotate(0)'}`
    )

  //Animates annotations, to show slowly after the graph lines has finished
  //drawing
  annotationGroup
    .style('opacity', 0)
    .transition()
    .duration(transitionDuration)
    .style('opacity', 1)
}

/**
 * Renders a chart legend on the top left corner of the chart, the background
 * dynamically scales based on the longest legend text
 */
const renderLegend = <T extends LegendItem>({
  chartSvg,
  legendData,
  legendID,
}: {
  chartSvg: SvgSelection
  legendData: Array<T>
  legendID: string
}) => {
  if (select(`#${legendID}`)) {
    select(`#${legendID}`).remove()
  }

  //Creates SVG group for the legend
  const legend = chartSvg.append('g').attr('id', legendID)
  //Gap between legend items
  let gap = 0
  //Text bounding box
  let textBBox = {} as TextBoundaryBox
  let longestTextWidth = 0
  //Padding for the legend width and height
  const xPadding = 18

  if (legendData?.length > 0) {
    //Iterates trough the graph legend items
    legendData.forEach((legendItem) => {
      if (legendItem?.color !== Styling.hidden) {
        legend
          .append('circle')
          .attr('cx', 20)
          .attr('cy', 20 + gap)
          .attr('class', `legend-item-${legendItem?.id}`)
          .attr('r', 8)
          .style('stroke', legendItem?.color)
          .style('stroke-width', '2px')
          .style('fill', legendItem?.lineToggled ? legendItem?.color : 'white')
          .on('click touchend', () => legendItem?.onClick?.())

        legend
          .append('text')
          .attr('x', 32)
          .attr('y', 25 + gap)
          .text(legendItem?.text)
          .attr('class', `legend-item-${legendItem?.id}-text`)
          .attr('class', 'legend-item-text')
          .style('top', function () {
            textBBox = this.getBBox()
            //Update the largest width if necessary
            if (textBBox.width > longestTextWidth) {
              longestTextWidth = textBBox.width
            }
            return ''
          })
          .style(
            'text-decoration',
            legendItem?.lineToggled ? 'none' : 'line-through'
          )
          .on('click touchend', () => legendItem?.onClick?.())

        gap = gap + 26
      }
    })
  }

  //Renders legend background for the legend items
  legend
    .append('rect')
    .attr('id', 'legend-background')
    .attr('rx', 4)
    .attr('x', 1)
    .attr('y', 1)
    .attr('width', longestTextWidth + 2 * xPadding)
    .attr('height', gap + xPadding - 10)
    .style('fill', 'white')

  //Makes sure that the text and the legend circles always have the highest
  //z-index on the svg layer
  selectAll('text')?.raise()
  selectAll(`[class^='legend-item']`)?.raise()
}

/**
 * Moves the first tick text to be anchored at the start of the first guideline
 * and the last text tick to be anchored at the end of the last guideline, so
 * that they appear in the graph's container
 */
const indentFirstAndLastTickText = (
  xAxisClass: string,
  firstOnly?: boolean
) => {
  const firstLabel = selectAll(`.${xAxisClass} .tick text`)
  const firstTick = firstLabel.nodes()[0]
  const lastTick = firstLabel.nodes()[firstLabel.size() - 1]

  select(firstTick)
    .attr('id', 'income-start-age-first-tick')
    .style('text-anchor', 'start')
  if (!firstOnly) {
    select(lastTick).style('text-anchor', 'end')
  }
}

/**
 * Returns the element's computed width and height
 */
const getElementDimensions = (element: string) => {
  const computedStyle = select(element)

  return {
    width: Number.parseInt(computedStyle?.style('width')),
    height: Number.parseInt(computedStyle?.style('height')),
  }
}

/**
 * Draws a graph element to and attaches it to the graph. The graph element
 * contains a line chart of the data and area below the line chart.
 */
const drawGraphLineAndArea = <T>({
  svg,
  yScaledData,
  xScaledData,
  scaledYStop,
  stylingOptions,
  lineKey,
  areaKey,
  data,
}: DrawGraphLineAndAreaParams<T>) => {
  const generateScaledChartLine = chart?.drawChartLine(xScaledData, yScaledData)

  const generateScaledArea = chart?.drawArea(
    xScaledData,
    yScaledData,
    scaledYStop as unknown as (d: T) => number
  )

  //Give ids so the render order changes Render order is sequential
  chart?.renderChartLine({
    chartSvg: svg,
    id: lineKey,
    data,
    drawLine: generateScaledChartLine,
    strokeWidth: stylingOptions?.stroke,
    strokeColor: stylingOptions?.color,
    fill: stylingOptions?.fill,
    strokeDashArray: stylingOptions?.dashArray,
    arrowHead: stylingOptions?.arrowHead,
    transitionDuration: stylingOptions?.transitionDuration,
  })

  chart?.renderAreaLine({
    chartSvg: svg,
    id: areaKey,
    data,
    drawArea: generateScaledArea,
    fill: stylingOptions?.areaColor,
    opacity: stylingOptions?.areaOpacity,
    transitionDuration: stylingOptions?.transitionDuration,
  })

  return yScaledData
}

/**
 * Adds an svg circle to the provided x and y coordinates. The circle has
 * transition with duration of 3 seconds by default
 /**
 * Adds an svg circle to the provided x and y coordinates. The circle has
 * transition with duration of 3 seconds by default
 */
const drawCircleOnLine = ({
  chartSvg,
  xScaledPosition,
  yScaledPosition,
  textInCircle,
  stylingOptions,
  circleID,
  transitionDuration = 3_000,
  hideDot,
}: DrawCircleOnLineParams) => {
  //Remove previous circle group in an event of redrawing the graph Also makes
  //sure that there is only one circle group at the same time to stale data does
  //not appear
  if (chartSvg.select(`.circle-group-${circleID}`)) {
    chartSvg.select(`.circle-group-${circleID}`).remove()
  }

  if (chartSvg.select(`#${circleID}`)) {
    chartSvg.select(`#${circleID}`).remove()
    chartSvg.select(`.${circleID}-text`).remove()
  }

  const circleGroupSVG = chartSvg
    .append('g')
    .attr('class', `circle-group-${circleID}`)

  circleGroupSVG
    .append('text')
    .style('fill', stylingOptions?.textColor ?? 'white')
    .style('font-weight', '800')
    .attr('class', `${circleID}-text`)
    .attr('text-anchor', 'middle')
    .attr('x', xScaledPosition)
    .attr('y', yScaledPosition + 5)
    .text(textInCircle)

  circleGroupSVG
    .append('circle')
    .style('fill', stylingOptions?.color ?? 'red')
    .attr('cx', xScaledPosition)
    .attr('cy', yScaledPosition)
    .attr('r', stylingOptions?.radius || '13px')
    .style('stroke', 'white')

  //Animations for the circles
  circleGroupSVG
    .style('opacity', 0)
    .transition()
    .duration(transitionDuration)
    .style('opacity', hideDot ? 0 : 1)
}

/**
 * Renders an area for passed in data with styling options for the
 * area
 */
const renderAreaLine = <T>({
  chartSvg,
  id,
  data,
  drawArea,
  fill,
  opacity,
  transitionDuration = 1_500,
  containerWidth = 500,
}: RenderAreaLineParams<T>) => {
  //Remove previous area if there is a data update
  chartSvg.select(`#${id}`).remove()

  chartSvg
    .append('g')
    .attr('id', id)
    .selectAll('.line')
    .data([data])
    .join('path')
    .style('opacity', opacity ?? 1)
    .style('fill', fill ?? 'transparent')
    .transition()
    .duration(transitionDuration)
    .attr('d', (d) => drawArea(d))
    .attrTween(
      'clip-path',
      () => (e) => `inset(0 ${(1 - e) * containerWidth}px 0 0)`
    )
}

export const chart = {
  moveXticks,
  moveYticks,
  drawChartSvg,
  generateTimeScaleX,
  generateLinearScaleX,
  generateScaleY,
  drawAxisX,
  drawAxisY,
  drawChartLine,
  drawArea,
  appendAxisXtoGraph,
  appendAxisYtoGraph,
  renderChartLine,
  renderAreaLine,
  generateHoveringAnnotations,
  generateFocusCircle,
  removeGarbagePath,
  renderLegend,
  indentFirstAndLastTickText,
  getElementDimensions,
  drawGraphLineAndArea,
  drawCircleOnLine,
  crosshairAndAnnotation,
  addAnnotation,
}
