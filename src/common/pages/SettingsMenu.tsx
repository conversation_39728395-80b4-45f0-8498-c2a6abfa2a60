import { isDisabled } from '../../features/DisabledLaunchFeatures'
import { useAccountService } from '../../features/authentication/hooks/useAccountService'
import { PRIVATE, SETTINGS } from '../../routes/Route'
import { ASSET } from '../constants/Assets'
import { UI_TEST_ID } from '../constants/DataTestIDs'
import { useTranslate } from '../hooks/useTranslate'
import { MenuCardConfig } from '../types/SecondaryMenu.types'
import SecondaryMenu from './SecondaryMenu'

const menuCards: Array<MenuCardConfig> = [
  {
    title: '',
    variant: 'alternative',
    items: [
      {
        mainText: 'LANGUAGE_SETTINGS.PAGE_TITLE',
        to: SETTINGS.LANGUAGE,
        icon: ASSET.iconaccountmenulanuae,
        cardVariant: 'gray-dirty',
      },
    ],
  },
  {
    title: '',
    variant: 'alternative',
    items: [
      {
        to: SETTINGS.CLOSE_ACCOUNT,
        mainText: 'ACCOUNT.PAGE_TITLE_DELETE_ACCOUNT',
        icon: ASSET.deleteAccount,
        writeProtected: true,
        dataTestID: UI_TEST_ID.menuItemCloseAccount,
        disabled: isDisabled,
        cardVariant: 'gray-dirty',
      },
    ],
  },
  {
    title: '',
    variant: 'alternative',
    items: [
      {
        to: SETTINGS.PIN,
        mainText: 'ACCOUNT.MENU_ITEM_CREATE_PIN',
        icon: ASSET.iconaccountmenupin,
        // pin change is write protected, but first time pin setup is not
        writeProtected: false,
        dataTestID: UI_TEST_ID.menuItemPinChange,
        cardVariant: 'gray-dirty',
      },
    ],
  },
]

const SettingsPage = () => {
  const t = useTranslate()
  const {
    context: { user_details },
  } = useAccountService()

  return (
    <SecondaryMenu
      navigateTo={PRIVATE.ACCOUNT}
      pageTitle={t('SETTINGS.PAGE_TITLE')}
      menuCards={menuCards.map((card) => ({
        ...card,
        title: t(card.title),
        items: card.items.map((item) => ({
          ...item,
          mainText: t(item.mainText),
          writeProtected: item?.to?.includes('pin') && user_details?.pin_set,
        })),
      }))}
    />
  )
}

export default SettingsPage
