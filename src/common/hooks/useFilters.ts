import { useState } from 'react'
import { DefaultParamsUseFilters, RangeOption } from '../types/Filter.types'
import {
  filterArrayByDateRange,
  rangeTypeToRange,
} from '../utils/UtilFunctions'
import { DateRangeToNumber, filterRangeTypes } from '../utils/consts'
import { useTranslate } from './useTranslate'

/**
 * Contains `<Filters />` component state and logic. The filters
 * are used to filter an array by given date range
 */
export const useFilters = <T extends Array<unknown>>({
  defaultFromDate = '',
  defaultToDate = '',
  defaultRangeOption,
  onResetFilters,
  array,
  filterKey,
  onFiltersApplied,
}: DefaultParamsUseFilters<T>) => {
  const t = useTranslate()
  defaultRangeOption = {
    ...filterRangeTypes[4],
    label: t(filterRangeTypes[4].label),
  }
  const [rangeOption, setRangeOption] = useState(defaultRangeOption)
  const [fromDate, setFromDate] = useState({
    dateStringISO: defaultFromDate,
  })
  const [toDate, setToDate] = useState({
    dateStringISO: defaultToDate,
  })

  const resetFilters = () => {
    setFromDate({
      dateStringISO: defaultFromDate,
    })
    setToDate({
      dateStringISO: defaultToDate,
    })
    setRangeOption(defaultRangeOption)
    onFiltersApplied(array)
    onResetFilters?.()
  }

  const handleFromDate = (dateStringISO: string) => {
    const filteredArray = filterArrayByDateRange({
      arrayOfObjects: array,
      fromDate: dateStringISO,
      toDate: toDate?.dateStringISO,
      objectKey: filterKey?.range,
    })
    onFiltersApplied(filteredArray as T)
    setFromDate({ dateStringISO })
  }

  const handleToDate = (dateStringISO: string) => {
    const filteredArray = filterArrayByDateRange({
      arrayOfObjects: array,
      fromDate: fromDate?.dateStringISO,
      toDate: dateStringISO,
      objectKey: filterKey?.range,
    })

    onFiltersApplied(filteredArray as T)
    setToDate({ dateStringISO })
  }
  const handleRangeType = (option: RangeOption) => {
    setRangeOption(option)

    let from = fromDate?.dateStringISO
    let to = toDate?.dateStringISO

    if (option.type !== 'customRange') {
      const { from: fromDate, to: toDate } = rangeTypeToRange({
        rangeType: option.type,
        dateType: 'day',
        filterTypes: DateRangeToNumber,
      })
      from = fromDate
      to = toDate
    }

    const filteredArray = filterArrayByDateRange({
      arrayOfObjects: array,
      fromDate: from,
      toDate: to,
      objectKey: filterKey?.range,
    })

    onFiltersApplied(filteredArray as T)
  }

  return {
    fromDate,
    setFromDate,
    toDate,
    setToDate,
    resetFilters,
    rangeOption,
    setRangeOption,
    handleFromDate,
    handleToDate,
    handleRangeType,
  }
}
