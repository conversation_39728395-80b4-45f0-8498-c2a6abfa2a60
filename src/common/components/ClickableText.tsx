import style from '../style/ClickableText.module.scss'
import { ClickableTextProps } from '../types/ClickableText.types'

/**
 * Renders text and attaches a `onClick` event to a passed in text segment
 */
const ClickableText = ({
  text = '',
  onClick,
  clickableText = '',
  className,
  dataTestID,
}: ClickableTextProps) => {
  const index = text.indexOf(clickableText)

  const parts = [
    text.slice(0, index),
    text.slice(index, index + clickableText.length),
    text.slice(index + clickableText.length),
  ]
  if (parts && parts.length > 0) {
    return (
      <>
        {parts[0]}
        <span
          onClick={onClick}
          className={`${className ?? style[`clickableText--no-margin`]}`}
          data-testid={dataTestID}
        >
          {parts[1]}
        </span>
        {parts[2]}
      </>
    )
  }

  return <></>
}

export default ClickableText
