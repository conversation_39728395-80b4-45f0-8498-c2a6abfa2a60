import { TimerButtonProps } from '../types/TimerButton.types'
import Button from './Button'
import CountDownTimer from './CountDownTimer'

/**
 * Button that displays a countdown timer next to the button, only
 * one parameter of `hours`, `minutes` or `seconds needs to be passed to start
 * the countdown
 */
const TimerButton = ({
  hours,
  minutes,
  seconds,
  disabled,
  children,
  onClick,
  onCountdownFinished,
  trackActivity,
  variant,
  loading,
  ...props
}: TimerButtonProps) => {
  return (
    <Button
      {...props}
      loading={loading}
      disabled={disabled}
      onClick={onClick}
      variant={variant}
      trackActivity={trackActivity}
    >
      {!disabled ? (
        <>{children}</>
      ) : (
        <>
          {children} (
          <CountDownTimer
            hours={hours}
            minutes={minutes}
            seconds={seconds}
            onCountdownFinished={onCountdownFinished}
          />
          )
        </>
      )}
    </Button>
  )
}

export default TimerButton
