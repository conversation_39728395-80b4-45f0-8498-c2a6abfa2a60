import dayjs from 'dayjs'
import { AgeMonth } from '../../features/CommonState.type'
import { useAccountService } from '../../features/authentication/hooks/useAccountService'
import { useSupportedCountries } from '../hooks/useSupportedCountries'
import { useTranslate } from '../hooks/useTranslate'
import { DateDropdownProps } from '../types/DateDropdown.types'
import {
  calculateRetirementValues,
  destructToNumbers,
} from '../utils/UtilFunctions'
import { DEFAULT_YEAR_MAX, DEFAULT_YEAR_MIN } from '../utils/consts'
import DateDropdown from './DateDropdown'
import InputLabel from './InputLabel'
import TextInput from './TextInput'

/**
 * Date of birth component with max and min age restrictions
 *
 * Default `USA`
 * Max age is 84 years old and 11 months
 * Minimum age is 18 years old
 *
 * Other max and min depend on the supported country
 */
const DateOfBirthInput = ({
  onChange,
  value,
  readOnly,
  ...rest
}: DateDropdownProps) => {
  const t = useTranslate()

  const {
    context: { user_details },
  } = useAccountService()

  const {
    supportedCountry: {
      tontinatorParams: { maxCurrentAge },
    },
  } = useSupportedCountries({
    alpha3CountryCode: user_details?.residency,
  }) as unknown as {
    supportedCountry: {
      tontinatorParams: {
        maxCurrentAge: AgeMonth
      }
    }
  }

  const CURRENT_YEAR = dayjs().year()
  /** +1 So month isn't zero indexed */
  const CURRENT_MONTH = dayjs().month() + 1
  const CURRENT_DAY = dayjs().date()

  const maxYear = CURRENT_YEAR - 18

  const day = destructToNumbers(value)?.day
  const year = destructToNumbers(value)?.year
  const month = destructToNumbers(value)?.month

  const dayTo =
    year === maxYear && month === dayjs().month() ? dayjs().date() : undefined
  const monthTo = year === maxYear ? dayjs().month() : undefined

  const currentAgeLabel = `${t('DATE_INPUT_SUFFIX_TEXT', {
    ageInYears: calculateRetirementValues(
      {
        date_of_birth: value,
      },
      {
        year: CURRENT_YEAR,
        month: CURRENT_MONTH,
        day: CURRENT_DAY,
      }
    )?.age,
  })}`

  // Format date based on user's residency
  const formatDateByResidency = (
    day?: number,
    month?: number,
    year?: number
  ) => {
    if (!day || !month || !year) return ''
    const formattedDay = day?.toString().padStart(2, '0')
    const formattedMonth = month?.toString().padStart(2, '0')

    // US format: MM/DD/YYYY
    if (
      user_details?.residency === 'USA' ||
      user_details?.residency === 'CAN' ||
      user_details?.residency === 'BLZ'
    ) {
      return `${formattedMonth}/${formattedDay}/${year}`
    }
    if (
      user_details?.residency === 'JPN' ||
      user_details?.residency === 'KOR' ||
      user_details?.residency === 'TWN'
    ) {
      // ISO format: YYYY-MM-DD
      return `${year}-${formattedMonth}-${formattedDay}`
    }

    // Default UK/EU format: DD/MM/YYY
    return `${formattedDay}/${formattedMonth}/${year}`
  }

  return (
    <>
      {readOnly ? (
        <>
          <article
            style={{
              position: 'relative',
            }}
          >
            <InputLabel label={t('INPUT_LABEL.DATE_OF_BIRTH')} />
            <TextInput
              label={currentAgeLabel}
              alternateLabel={true}
              value={formatDateByResidency(day, month, year)}
              readOnly
            />
          </article>
        </>
      ) : (
        <DateDropdown
          {...rest}
          yearTo={Number(CURRENT_YEAR - 18) || DEFAULT_YEAR_MIN}
          yearFrom={
            Number(CURRENT_YEAR - maxCurrentAge?.age) || DEFAULT_YEAR_MAX
          }
          monthTo={monthTo}
          dayTo={dayTo}
          onChange={onChange}
          value={value}
          yearLabel={currentAgeLabel}
        />
      )}
    </>
  )
}

export default DateOfBirthInput
