import { UI_TEST_ID } from '../../constants/DataTestIDs'
import style from '../../style/Card.module.scss'
import { CardFooterProps } from '../../types/Card.types'
import Button from '../Button'
import CardAlert from './CardAlert'
import CardArrow from './CardArrow'

/**
 * Renders the footer section for the `Card` component.
 */
const CardFooter = ({
  alertAndArrowPosition,
  alert,
  variant,
  ctaButtonLabel,
  ctaButtonVariant,
  onClick,
  rotateArrow,
  showArrow,
  arrowInvisible,
  secondaryIcon,
  extendFooter,
}: CardFooterProps) => (
  <div
    data-testid={UI_TEST_ID?.cardFooter}
    className={`${style[`card__footer`]} ${style[`card__footer${variant ? `--${variant}` : ''}`]}`}
  >
    {alertAndArrowPosition === 'end' && (
      <>
        {extendFooter}
        <CardAlert alert={alert ?? 0} />
        {(showArrow || arrowInvisible) && (
          <CardArrow
            rotateArrow={rotateArrow}
            variant={variant}
            arrowInvisible={arrowInvisible}
            secondaryIcon={secondaryIcon}
          />
        )}
      </>
    )}
    {ctaButtonLabel && (
      <Button variant={ctaButtonVariant} onClick={onClick}>
        {ctaButtonLabel}
      </Button>
    )}
  </div>
)
export default CardFooter
