import { UI_TEST_ID } from '../../constants/DataTestIDs'
import { ConfirmationModalProps } from '../../types/ConfirmationModal.types'

import style from '../../style/ConfirmationModal.module.scss'
import Modal from '../Modal'
import ConfirmationModalContent from './ConfirmationModalContent'
import ConfirmationModalHeader from './ConfirmationModalHeader'

/**
 * Renders a confirmation modal with a header and content section.
 * The header can display an icon or animated icon along with a translated title.
 * The content section supports translated text and optional child components.
 * Wrapped in a Modal component, it handles backdrop rendering and dialog display.
 */
const ConfirmationModal = ({
  isOpen,
  title,
  content,
  icon,
  animatedIcon,
  contentValues,
  children,
  dataTestID = UI_TEST_ID.confirmationModal,
  backdrop = true,
  ...rest
}: ConfirmationModalProps) => (
  <Modal
    {...rest}
    isOpen={Boolean(isOpen)}
    dataTestID={dataTestID}
    backdrop={backdrop}
  >
    <section className={style.confirmationModal}>
      <ConfirmationModalHeader
        icon={icon}
        animatedIcon={animatedIcon}
        title={title}
        contentValues={contentValues}
      />

      <ConfirmationModalContent content={content} contentValues={contentValues}>
        {children}
      </ConfirmationModalContent>
    </section>
  </Modal>
)

export default ConfirmationModal
