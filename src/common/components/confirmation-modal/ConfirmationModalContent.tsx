import { Trans } from 'react-i18next'
import {
  ConfirmationModalBaseProps,
  ConfirmationModalContentProps,
} from '../../types/ConfirmationModal.types'

import style from '../../style/ConfirmationModal.module.scss'

/**
 * Renders the content section of a confirmation modal.
 * Displays a translated message if `content` is provided, followed by any child components.
 */
const ConfirmationModalContent = ({
  content,
  contentValues,
  children,
}: ConfirmationModalContentProps &
  Pick<ConfirmationModalBaseProps, 'contentValues' | 'children'>) => (
  <>
    {content && (
      <div className={style.confirmationModal__explainer}>
        <Trans i18nKey={content} values={contentValues} />
      </div>
    )}
    <div className={style.confirmationModal__children_wrapper}>{children}</div>
  </>
)

export default ConfirmationModalContent
