import { useFileUpload } from '../hooks/useFileUpload'
import style from '../style/FileUpload.module.scss'
import { FileUploadProps } from '../types/FileUpload.types'

/**
 * File upload component with drag and drop functionality
 */
const FileUpload = ({
  onFilesSelected,
  onDragStateChange,
  accept,
  multiple = false,
  className = '',
  children,
  ...props
}: FileUploadProps) => {
  const {
    isDragging,
    fileInputRef,
    handleDrag,
    handleDragIn,
    handleDragOut,
    handleDrop,
    handleFileInput,
    triggerFileInput,
  } = useFileUpload(onFilesSelected, onDragStateChange)
  return (
    <article
      className={`
      ${style[`file-upload`]} 
      ${style[`file-upload${isDragging ? '--dragging' : ''}`]} 
      ${className}
      `}
      onDragEnter={handleDragIn}
      onDragLeave={handleDragOut}
      onDragOver={handleDrag}
      onDrop={handleDrop}
      onClick={triggerFileInput}
      // biome-ignore lint/a11y/noNoninteractiveElementToInteractiveRole: <explanation>
      // biome-ignore lint/a11y/useSemanticElements: <explanation>
      role="button"
      tabIndex={0}
    >
      <input
        {...props}
        type="file"
        ref={fileInputRef}
        style={{ display: 'none' }}
        accept={accept}
        multiple={multiple}
        onChange={handleFileInput}
      />
      {children}
    </article>
  )
}

export default FileUpload
