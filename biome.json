{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true, "defaultBranch": "staging"}, "files": {"ignoreUnknown": false, "ignore": ["**/public/**/*", "**/__test__/**/*", "my-tontine/dist/**/*", "facetec-web-sdk/**/*", ".vscode/**/*"]}, "formatter": {"enabled": true, "useEditorconfig": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 80, "attributePosition": "auto", "bracketSpacing": true}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "complexity": {"noForEach": "off", "useLiteralKeys": "off"}, "correctness": {"noUndeclaredVariables": "error", "noUnusedVariables": "error", "noUnusedImports": "error", "useArrayLiterals": "error", "useExhaustiveDependencies": "off"}, "style": {"noUnusedTemplateLiteral": "off", "useNodejsImportProtocol": "off", "useImportType": "off", "noParameterAssign": "warn", "useBlockStatements": "off", "noNamespace": "error", "noParameterProperties": "error"}, "suspicious": {"noEmptyBlockStatements": "error", "noArrayIndexKey": "off", "noGlobalIsNan": "off"}, "nursery": {"noNestedTernary": "error"}, "a11y": {"useKeyWithClickEvents": "off"}}, "ignore": ["**/public/**/*", "**/__test__/**/*", "my-tontine/dist/**/*", "facetec-web-sdk/**/*", ".vscode/**/*", "**/.netlify/**/*", "**/coverage/**/*", "**/coverage-ts/**/*", "**/*.d.ts"]}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "es5", "semicolons": "asNeeded", "arrowParentheses": "always", "bracketSameLine": false, "quoteStyle": "single"}, "globals": ["JSX", "React", "InstallTrigger"]}, "overrides": [{"include": ["cypress/**/*.ts"], "linter": {"rules": {"suspicious": {"noDebugger": "off"}}}}, {"include": ["**/*.jsx", "**/*.tsx"], "linter": {"rules": {"correctness": {"useHookAtTopLevel": "error"}}}}, {"include": ["cypress/**/*"], "javascript": {"globals": ["<PERSON><PERSON><PERSON><PERSON>", "cy", "expect", "describe", "it", "mount", "context", "wrap", "beforeEach", "Cypress", "sinon"]}}]}